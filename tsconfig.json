{
  "compilerOptions": {
    "target": "ES2020",
    // Target modern JavaScript
    "module": "CommonJS",
    // Standard module system for Node.js libraries
    "lib": [
      "ES2020"
    ],
    "declaration": true,
    // Generate .d.ts files
    "declarationMap": true,
    // Generate source maps for .d.ts files
    "sourceMap": true,
    // Generate .js.map source map files
    "outDir": "./dist",
    // Output directory for compiled files
    "rootDir": "./src",
    // Root directory of source files
    "strict": true,
    // Enable all strict type-checking options
    "esModuleInterop": true,
    // Enables compatibility with CommonJS modules
    "skipLibCheck": true,
    // Skip type checking of declaration files
    "forceConsistentCasingInFileNames": true,
    // Disallow inconsistently-cased references to the same file.
    "moduleResolution": "node"
    // How modules get resolved (classic or node)
  },
  "include": [
    "src/**/*"
  ],
  // Which files to include in compilation
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts"
  ]
  // Which files to exclude
}